// src/components/SkillCategories.jsx
import { motion } from 'framer-motion';
import { ChevronRight, TrendingUp } from 'lucide-react';

const SkillCategories = ({ categories, skills, onCategorySelect, selectedCategory }) => {
  // Get skill count for each category
  const getCategoryStats = (categoryId) => {
    const categorySkills = skills.filter(skill => skill.category === categoryId);
    const completed = categorySkills.filter(skill => skill.progress === 100).length;
    const inProgress = categorySkills.filter(skill => skill.progress > 0 && skill.progress < 100).length;
    const avgProgress = categorySkills.length > 0 
      ? Math.round(categorySkills.reduce((sum, skill) => sum + skill.progress, 0) / categorySkills.length)
      : 0;
    
    return {
      total: categorySkills.length,
      completed,
      inProgress,
      avgProgress
    };
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Skill Categories</h3>
        <button
          onClick={() => onCategorySelect('all')}
          className={`text-sm px-3 py-1 rounded-full transition-all duration-200 ${
            selectedCategory === 'all'
              ? 'bg-primary-500 text-white'
              : 'text-white/60 hover:text-white hover:bg-white/10'
          }`}
        >
          View All
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category, index) => {
          const stats = getCategoryStats(category.id);
          const isSelected = selectedCategory === category.id;

          return (
            <motion.div
              key={category.id}
              className={`card-glass p-6 cursor-pointer group relative overflow-hidden ${
                isSelected ? 'ring-2 ring-primary-500' : ''
              }`}
              whileHover={{ y: -2, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => onCategorySelect(category.id)}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
              
              {/* Header */}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${category.color} flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-200`}>
                    {category.icon}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white group-hover:text-primary-300 transition-colors duration-200">
                      {category.name}
                    </h4>
                    <p className="text-sm text-white/60">
                      {stats.total} skill{stats.total !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <ChevronRight 
                  size={20} 
                  className={`text-white/40 group-hover:text-white transition-all duration-200 ${
                    isSelected ? 'rotate-90' : 'group-hover:translate-x-1'
                  }`} 
                />
              </div>

              {/* Progress Bar */}
              <div className="mb-4 relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-white/60">Average Progress</span>
                  <span className="text-sm font-medium text-white">{stats.avgProgress}%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
                  <motion.div
                    className={`h-full bg-gradient-to-r ${category.color} rounded-full`}
                    initial={{ width: 0 }}
                    animate={{ width: `${stats.avgProgress}%` }}
                    transition={{ duration: 1, delay: 0.2 + index * 0.1 }}
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-2 relative z-10">
                <div className="text-center p-2 bg-white/5 rounded-lg">
                  <div className="text-lg font-bold text-green-400">{stats.completed}</div>
                  <div className="text-xs text-white/50">Done</div>
                </div>
                <div className="text-center p-2 bg-white/5 rounded-lg">
                  <div className="text-lg font-bold text-yellow-400">{stats.inProgress}</div>
                  <div className="text-xs text-white/50">Active</div>
                </div>
                <div className="text-center p-2 bg-white/5 rounded-lg">
                  <div className="text-lg font-bold text-blue-400">{stats.total - stats.completed - stats.inProgress}</div>
                  <div className="text-xs text-white/50">New</div>
                </div>
              </div>

              {/* Trending Indicator */}
              {stats.inProgress > 0 && (
                <div className="absolute top-4 right-4 flex items-center gap-1 bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs">
                  <TrendingUp size={12} />
                  Active
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Quick Add Popular Skills */}
      <div className="mt-8">
        <h4 className="text-lg font-semibold text-white mb-4">Popular Skills to Add</h4>
        <div className="flex flex-wrap gap-2">
          {categories.slice(0, 3).map(category => 
            category.skills.slice(0, 3).map((skill, index) => (
              <motion.button
                key={`${category.id}-${skill}`}
                className="px-3 py-2 bg-white/10 text-white/80 rounded-lg hover:bg-white/20 hover:text-white transition-all duration-200 text-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {category.icon} {skill}
              </motion.button>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default SkillCategories;
